{%- from 'components/hero/hero.njk' import HeroHome -%}
{%- from 'views/utils/utils.njk' import getScriptPath, getStylePath -%}
{%- from 'components/quicklinks/quicklinks.njk' import QuickLinksHome -%}
{%- from 'components/news/news.njk' import NewsHome -%}
{%- from 'components/events/events.njk' import EventsHome -%}
{%- from 'components/editions/editions.njk' import EditionsHome -%}
{%- from 'components/location/location.njk' import LocationHome -%}
{%- from 'components/discover/discover.njk' import DiscoverHome -%}
{%- from 'components/banner/banner.njk' import BannerHome -%}
{%- from 'components/presentation/presentation.njk' import PresentationKeyNumberHome -%}
{%- from 'components/custom-map/custom-map.njk' import customMap -%}

{# Page override config #}
{% set pageConfig = {
    title: 'Home page',
    template: 'home',
    bodyClass: 'home-page',
    bundle: 'home'
} %}

{% extends 'views/layouts/base.njk' %}

{% block siteComponents %}{% endblock %}

{% block main %}
    {# {{ HeroHome('type-3') }}
    {{ QuickLinksHome() }}
    {{ PresentationKeyNumberHome() }}
    {{ NewsHome() }}
    {{ EventsHome(banner=false, proposerButton=false, quicklinks=false) }}
    {{ BannerHome() }}
    {{ BannerHome(isReverse = true) }}
    {{ BannerHome(isInverted = true, bgColor = '#000000') }}
    {{ BannerHome(isReverse = true, isInverted = true, bgColor = '#000000') }}
    {{ EditionsHome() }}
    {{ DiscoverHome() }} #}
    
    {{ customMap() }}
    {# {{ LocationHome(itemsCount = 3) }} #}

{% endblock %}

