@use 'sass:math';

.custom-map-wrapper {
    position: relative;
    overflow: hidden;
}

@keyframes scaling {
    from {
        transform: scale(1);
    }

    to {
        transform: scale(1.3);
    }
}

.custom-map {
    @include size(100%, 85rem);
    position: relative;

    @include breakpoint(medium down) {
        height: 44rem;
    }

    @include breakpoint(small down) {
        height: 41rem;
    }

    &__btn {
        $circle-size: 60.3rem;
        @include trs($delay: 300ms);
        @include icon-before($fa-var-transporter-2);
        @include absolute(0, null, null, 0);
        @include size(100%);
        @include focus-outline($offset: -3px);
        background-size: cover;
        background-position: center;
        border: 0;
        cursor: pointer;
        display: block;
        margin: 0;
        opacity: 1;
        padding: 0;
        perspective: 1000px;
        transform: scale(1);
        visibility: visible;

        &::before,
        &::after {
            @include trs();
            @include absolute(50%, null, null, 50%);
            @include size($circle-size);
            display: block;
            margin-left: -#{math.div($circle-size, 2)};
            margin-top: -#{math.div($circle-size, 2)};
        }

        &::before {
            animation: scaling 3s linear infinite alternate;
            color: $color-1--1;
            font-size: 50.3rem;
            line-height: $circle-size;
            z-index: 1;
        }

        &::after {
            background: radial-gradient(at 50% 50%, #000000 0%, rgba(0, 0, 0, 0.34) 59%, rgba(0, 0, 0, 0.13) 81%, rgba(0, 0, 0, 0) 100%);
            border-radius: 50%;
            content: '';
            opacity: 0.5;
        }

        @include breakpoint(medium down) {
            &::before,
            &::after {
                @include size(40rem);
                margin-left: -20rem;
                margin-top: -20rem;
            }

            &::before {
                font-size: 40rem;
                line-height: 40rem;
            }
        }

        @include breakpoint(small down) {
            &::before,
            &::after {
                @include size(20rem);
                margin-left: -10rem;
                margin-top: -10rem;
            }

            &::before {
                font-size: 20rem;
                line-height: 20rem;
            }
        }

        &.-is-hidden {
            opacity: 0;
            visibility: hidden;

            &::before,
            &::after {
                opacity: 0;
                visibility: hidden;
            }
        }
    }

    canvas {
        @include size(100%);
    }

    div:not([class]) {
        position: absolute;
    }
}

.custom-map-data {
    display: none;
}

.custom-map-title {
    @include trs($duration: 700ms);
    @include absolute(10rem, null, null, 50%);
    color: $color-2--1;
    font-family: var(--typo-1);
    font-size: 5.5rem;
    font-weight: var(--fw-bold);
    line-height: 1;
    margin: 0;
    padding-left: 3.2rem;
    transform: translateY(3rem);
    text-transform: uppercase;
    z-index: 9;
    opacity: 0;

    @include breakpoint(medium down) {
        font-size: 2.6rem;
        padding: 0 1rem;
        transform: translateX(-50%) translateY(2rem);
        text-align: center;
        top: 11rem;
        width: 100%;
    }

    //&::before {
    //    @include trs($duration: 1000ms, $delay: 2000ms);
    //    @include size(0.1rem, 0);
    //    @include absolute(0, null, null, 0);
    //    background-color: rgba($color-white, 0.5);
    //    content: '';
    //
    //    @include breakpoint(medium down) {
    //        content: none;
    //    }
    //}

    span {
        @include trs($duration: 500ms, $delay: 1000ms);
        display: block;
        font-size: 4.5rem;
        font-weight: var(--fw-light);
        opacity: 0;
        transform: translateY(-3rem);

        @include breakpoint(medium down) {
            display: inline-block;
            font-size: inherit;
            margin-right: 0.5rem;
        }
    }

    &.-is-visible {
        opacity: 1;
        transform: translateY(0);
        visibility: visible;

        @include breakpoint(medium down) {
            transform: translateX(-50%) translateY(0);
        }

        //&::before {
        //    height: 28rem;
        //}

        span {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

.custom-map-pictogram {
    display: none;

    @include breakpoint(medium down) {
        @include trs();
        @include absolute(2rem, null, null, 1rem);
        @include size(2.7rem, 3.1rem);
        display: block;
        opacity: 0;
        visibility: hidden;
        z-index: 9;

        img {
            @include size(100%);
            display: block;
        }
    }

    &.-is-visible {
        opacity: 1;
        transform: translateY(0);
        visibility: visible;
    }
}

.custom-map-popup {
    @include trs();
    @include absolute(0, 0);
    @include size(40rem, 100%);
    @include flex(row, wrap, null, center, center);
    overflow-y: auto;
    overflow-x: hidden;
    background-color: rgba($color-white, 0.9);
    transform: translateX(100%);
    z-index: 10;

    @include breakpoint(small down) {
        @include size(100%, 100vh);
        position: fixed;
        max-width: 100%;
        background-color: $color-white;
    }

    &.-is-active {
        transform: translateX(0);
    }

    .custom-map:not(.-is-active) & {
        display: none;
    }

    &__close {
        @include trs();
        @include size(3rem);
        background-color: $color-1--1;
        border: 0;
        color: $color-white;
        cursor: pointer;
        font-size: 1.6rem;
        position: absolute;
        right: 0;
        top: 0;

        &[data-fa-icon]::before {
            color: inherit;
            font-size: inherit;
            margin: 0;
        }

        @include on-event {
            background-color: $color-1--2;
        }
    }

    &__content {
        flex-basis: auto;
        flex-grow: 1;
        height: 100%;
    }
}

.custom-map-content {
    @include flex(row, wrap, null, center);

    &__inner {
        flex-grow: 1;
        flex-basis: auto;
        padding: 5rem;

        @include breakpoint(small down) {
            padding: 3rem 4rem;
        }
    }

    &__button {
        align-self: flex-end;
        flex-grow: 0;
        flex-shrink: 1;

        .btn {
            &::after {
                background-color: transparent;
            }
        }
    }

    &__title {
        border-bottom: 2px solid $color-1--1;
        color: $color-2--1;
        font-family: var(--typo-1);
        font-size: 3rem;
        font-weight: var(--fw-light);
        margin: 0 0 3rem;
        padding: 0 0 3rem;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 2.2rem;
            margin-bottom: 1.7rem;
            padding-bottom: 1.7rem;
        }

        span {
            display: block;
            font-weight: var(--fw-bold);
        }
    }

    &__teaser {
        color: $color-2--1;
        font-family: var(--typo-1);
        font-size: 2rem;
        font-style: italic;
        font-weight: var(--fw-light);
        margin: 2rem 0;

        @include breakpoint(small down) {
            font-size: 1.8rem;
            line-height: 1.2;
        }
    }

    &__body {
        border-bottom: 2px solid $color-1--1;
        padding: 0 0 3rem;

        @include breakpoint(small down) {
            border-bottom: 0;
            padding: 0;
        }

        &.rte {
            p, li {
                color: $color-black;
                font-size: 1.8rem;

                @include breakpoint(small down) {
                    font-size: 1.6rem;
                    line-height: 1.2;
                }
            }

            ul > li::before {
                color: $color-3--4;
            }
        }
    }
}

.panolens-infospot {
    @include breakpoint(small down) {
        display: none !important;
    }
}

.infospot-content {
    background-color: $color-1--2;
    color: $color-white;
    font-size: 1.5rem;
    margin-left: 10rem;
    padding: 2rem 2.5rem;
    //width: 24.5rem;

    .panolens-infospot & {
        position: static;
        transform: translateY(-30%)
    }

    ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
            line-height: 1.2;
            padding-left: 1.3rem;
            position: relative;
            @include icon-before($fa-var-angle-right);

            &::before {
                position: absolute;
                left: 0;
            }
        }
    }

    // Styles pour les infospots sur image statique
    .custom-map-infospot {
        @include trs();
        position: absolute;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: $color-1--1;
        border: 2px solid $color-white;
        cursor: pointer;
        transform: translate(-50%, -50%);
        z-index: 10;

        &:hover {
            transform: translate(-50%, -50%) scale(1.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }
    }
}

