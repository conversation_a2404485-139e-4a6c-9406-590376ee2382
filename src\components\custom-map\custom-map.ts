import * as helpers from './helpers';

export default function initCustomMap(this: any) {
    // 360deg image customMap
    const customMapWrapper = document.querySelector('.js-custom-map-wrapper');
    // let PANOLENS: any;
    
    if (!customMapWrapper) {
        return;
    }

    const customMap = customMapWrapper.querySelector('.js-custom-map');

    if (!customMap) {
        return;
    }

    const button = customMap?.querySelector('.js-custom-map-button');

    const stopProp = (event) => {
        if (event.target.tagName.toLowerCase() === 'a') {
            event.stopPropagation();
        }
    };

    const createCustomMap = (customMapContainer, event) => {
        const target = event.target;

        if (!target) {
            return;
        }

        const customMapSource = target.getAttribute('data-custom-map');

        target.setAttribute('tabindex', '-1');
        helpers.addClass(target, '-is-hidden');

        helpers.addClass(customMap, '-is-active');

        if (customMapSource) {
            // Créer une image statique au lieu d'un panorama
            const imageElement = document.createElement('img');
            imageElement.src = customMapSource;
            imageElement.style.width = '100%';
            imageElement.style.height = '100%';
            imageElement.style.objectFit = 'cover';
            imageElement.style.position = 'relative';

            // Vider le container et ajouter l'image statique
            customMapContainer.innerHTML = '';
            customMapContainer.appendChild(imageElement);

            const data = customMapContainer.parentElement.querySelector('.js-custom-map-data');
            const popup = customMapContainer.parentElement.querySelector('.js-custom-map-popup');
            const title = customMapContainer.parentElement.querySelector('.js-custom-map-title');
            const picto = customMapContainer.parentElement.querySelector('.js-custom-map-pictogram');
            let initialized = false;

            popup.addEventListener('touchstart', stopProp);

            if (title) {
                helpers.addClass(title, '-is-visible');
            }

            if (picto) {
                helpers.addClass(picto, '-is-visible');
            }

            // Pas besoin de viewer PANOLENS pour une image statique
            // const viewer = new PANOLENS.Viewer({...});

            const showPopup = (e) => {
                const element = e.target;

                if (element) {
                    let wrapper;

                    // Pour une image statique, on cherche directement l'élément
                    wrapper = helpers.findAncestor(element, 'js-infospot-title') || element;

                    const targetId = wrapper.getAttribute('data-infospot-target');
                    const infospot = document.getElementById(targetId);

                    if (infospot) {
                        const container = popup.querySelector('.js-custom-map-popup-content');
                        const content = infospot.querySelector('.js-infospot-content');

                        if (container && content) {
                            container.innerHTML = content.innerHTML;
                            helpers.addClass(popup, '-is-active');
                            helpers.setTabindex(popup, 0);

                            if (window.innerWidth < 768) {
                                helpers.addClass(document.body, '-overflow-hidden');
                            }
                        }
                    }
                }
            };

            const hidePopup = (e) => {
                const element = e.target;

                initialized = true;

                if (element) {
                    const shouldClose = helpers.hasClass(element, 'js-custom-map-popup-close') || !popup.contains(element);
                    const shouldOpen = helpers.hasClass(element, 'js-infospot-title') || !!helpers.findAncestor(element, 'js-infospot-title');

                    if (window.innerWidth > 767 && !shouldOpen && shouldClose) {
                        helpers.removeClass(popup, '-is-active');
                        helpers.setTabindex(popup, -1);
                    } else if (window.innerWidth < 768 && shouldClose && element.tagName.toLowerCase() !== 'img') {
                        helpers.removeClass(popup, '-is-active');
                        helpers.removeClass(document.body, '-overflow-hidden');
                        helpers.setTabindex(popup, -1);
                    }
                }
            };

            if (data) {
                const infospots = [...data.querySelectorAll('.js-infospot')];

                if (infospots.length) {
                    infospots.forEach((infospot) => {
                        const { infospotImage, infospotCoords } = infospot.dataset;
                        const [x, y] = infospotCoords.split(','); // On utilise seulement x et y pour une image 2D
                        const id = infospot.getAttribute('id');
                        const infospotTitle = infospot.querySelector('.js-infospot-title');

                        // Créer un point d'intérêt HTML au lieu d'un infospot PANOLENS
                        const infospotElement = document.createElement('div');
                        infospotElement.className = 'custom-map-infospot';
                        infospotElement.style.position = 'absolute';
                        infospotElement.style.left = `${x}%`;
                        infospotElement.style.top = `${y}%`;
                        infospotElement.style.width = '20px';
                        infospotElement.style.height = '20px';
                        infospotElement.style.borderRadius = '50%';
                        infospotElement.style.backgroundColor = '#007bff';
                        infospotElement.style.border = '2px solid white';
                        infospotElement.style.cursor = 'pointer';
                        infospotElement.style.transform = 'translate(-50%, -50%)';
                        infospotElement.style.zIndex = '10';
                        infospotElement.setAttribute('data-infospot-target', id);

                        // Ajouter l'image de l'infospot si elle existe
                        if (infospotImage) {
                            const img = document.createElement('img');
                            img.src = infospotImage;
                            img.style.width = '100%';
                            img.style.height = '100%';
                            img.style.borderRadius = '50%';
                            img.style.objectFit = 'cover';
                            infospotElement.appendChild(img);
                        }

                        // Ajouter l'élément au container de l'image
                        customMapContainer.style.position = 'relative';
                        customMapContainer.appendChild(infospotElement);

                        // Gérer les événements de clic
                        infospotElement.addEventListener('click', showPopup);

                        if (infospotTitle) {
                            // Ajouter le titre comme tooltip
                            infospotElement.title = infospotTitle.textContent || '';

                            if (popup) {
                                if (helpers.hasClass(popup, '-is-active')) {
                                    helpers.setTabindex(popup, -1);
                                }
                                // Gérer les événements de clic et touch
                                ['click', 'touchstart'].forEach((eventType) => {
                                    infospotElement.addEventListener(eventType, showPopup);
                                });
                            }
                        }
                    });

                    if (popup) {
                        const closeButton = popup.querySelector('.js-custom-map-popup-close');

                        if (closeButton) {
                            ['touchstart'].forEach((eventType) => {
                                closeButton.addEventListener(eventType, hidePopup);
                            });
                        }

                        ['click', 'touchstart'].forEach((eventType) => {
                            document.addEventListener(eventType, hidePopup);
                        });
                    }
                }
            }
        }
    };

    if (button) {
        button.addEventListener('click', createCustomMap.bind(this, customMap));
    }
}
