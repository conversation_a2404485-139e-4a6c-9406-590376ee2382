import * as helpers from './helpers';

// Declare PANOLENS as a global variable
declare const PANOLENS: any;

export default function initCustomMap(this: any) {
    // 360deg image customMap
    const customMapWrapper = document.querySelector('.js-custom-map-wrapper');

    if (!customMapWrapper) {
        return;
    }

    const customMap = customMapWrapper.querySelector('.js-custom-map');

    if (!customMap && !PANOLENS) {
        return;
    }

    const button = customMap?.querySelector('.js-custom-map-button');

    const stopProp = (event) => {
        if (event.target.tagName.toLowerCase() === 'a') {
            event.stopPropagation();
        }
    };

    const createCustomMap = (customMapContainer, event) => {
        const target = event.target;

        if (!target) {
            return;
        }

        const customMapSource = target.getAttribute('data-custom-map');

        target.setAttribute('tabindex', '-1');
        helpers.addClass(target, '-is-hidden');

        helpers.addClass(customMap, '-is-active');

        if (customMapSource) {
            const image = new PANOLENS.ImagePanorama(customMapSource);

            const data = customMapContainer.parentElement.querySelector('.js-custom-map-data');
            const popup = customMapContainer.parentElement.querySelector('.js-custom-map-popup');
            const title = customMapContainer.parentElement.querySelector('.js-custom-map-title');
            const picto = customMapContainer.parentElement.querySelector('.js-custom-map-pictogram');
            let initialized = false;

            popup.addEventListener('touchstart', stopProp);

            if (title) {
                helpers.addClass(title, '-is-visible');
            }

            if (picto) {
                helpers.addClass(picto, '-is-visible');
            }

            const viewer = new PANOLENS.Viewer({
                container: customMapContainer,
                controlBar: true,
                controlButtons: ['fullscreen'],
                autoHideInfospot: false,
                autoHideControlBar: true,
                horizontalView: true,
                cameraFov: 65,
                autoRotate: true,
                autoRotateActivationDuration: 2000,
                autoRotateSpeed: 0.5,
                viewIndicator: false,
                output: 'none',
            });

            const showPopup = (e) => {
                const element = e.target;

                if (element) {
                    let wrapper;

                    if (element instanceof PANOLENS.Infospot) {
                        wrapper = helpers.findAncestor(element.element, 'js-infospot-title');
                    } else {
                        wrapper = helpers.findAncestor(element, 'js-infospot-title');
                    }

                    const infospot = document.getElementById(wrapper.getAttribute('data-infospot-target'));

                    if (infospot) {
                        const container = popup.querySelector('.js-custom-map-popup-content');
                        const content = infospot.querySelector('.js-infospot-content');

                        if (container && content) {
                            container.innerHTML = content.innerHTML;
                            helpers.addClass(popup, '-is-active');
                            helpers.setTabindex(popup, 0);
                            viewer.disableAutoRate();

                            if (window.innerWidth < 768) {
                                helpers.addClass(document.body, '-overflow-hidden');
                            }
                        }
                    }
                }
            };

            const hidePopup = (e) => {
                const element = e.target;

                if (initialized) {
                    viewer.disableAutoRate();
                }

                initialized = true;

                if (element) {
                    const shouldClose = helpers.hasClass(element, 'js-custom-map-popup-close') || !popup.contains(element);
                    const shouldOpen = helpers.hasClass(element, 'js-infospot-title') || !!helpers.findAncestor(element, 'js-infospot-title');

                    if (!helpers.hasClass(element, 'js-custom-map-popup-content') && !shouldOpen && shouldClose && !helpers.hasClass(popup, '-is-active')) {
                        setTimeout(() => {
                            if (!helpers.hasClass(popup, '-is-active')) {
                                viewer.enableAutoRate();
                            }
                        }, 3000);
                    }

                    if (helpers.hasClass(popup, '-is-active') && shouldClose && !shouldOpen) {
                        viewer.enableAutoRate();
                    }

                    if (window.innerWidth > 767 && !shouldOpen && shouldClose) {
                        helpers.removeClass(popup, '-is-active');
                        helpers.setTabindex(popup, -1);
                    } else if (window.innerWidth < 768 && shouldClose && element.tagName.toLowerCase() !== 'canvas') {
                        helpers.removeClass(popup, '-is-active');
                        helpers.removeClass(document.body, '-overflow-hidden');
                        helpers.setTabindex(popup, -1);
                    }
                }
            };

            if (data) {
                const infospots = [...data.querySelectorAll('.js-infospot')];

                if (infospots.length) {
                    infospots.forEach((infospot) => {
                        const { infospotImage, infospotCoords } = infospot.dataset;
                        const [x, y, z] = infospotCoords.split(',');
                        const id = infospot.getAttribute('id');
                        const infospotTitle = infospot.querySelector('.js-infospot-title');

                        // BECAUSE!!!
                        PANOLENS.DataImage[id] = infospotImage;

                        const panolensInfospot = new PANOLENS.Infospot(250, PANOLENS.DataImage[id], true);

                        // set infospot position
                        panolensInfospot.position.set(Number(x), Number(y), Number(z));

                        panolensInfospot.addEventListener('click', showPopup);

                        if (infospotTitle) {
                            panolensInfospot.addHoverElement(infospotTitle, 0);

                            // hide infospot when hover
                            panolensInfospot.addEventListener('hoverenter', () => {
                                if (window.innerWidth > 767) {
                                    panolensInfospot.material.opacity = 0;
                                }
                            });

                            // show infospot when hover out
                            panolensInfospot.addEventListener('hoverleave', () => {
                                if (window.innerWidth > 767) {
                                    panolensInfospot.material.opacity = 1;
                                }
                            });

                            if (popup) {
                                if (helpers.hasClass(popup, '-is-active')) {
                                    helpers.setTabindex(popup, -1);
                                }
                                // handle click on infospot hover element
                                ['click', 'touchstart'].forEach((eventType) => {
                                    panolensInfospot.element.addEventListener(eventType, showPopup);
                                    panolensInfospot.addEventListener(eventType, showPopup);
                                });
                            }
                        }

                        // add infospot to customMap
                        image.add(panolensInfospot);
                    });

                    if (popup) {
                        const closeButton = popup.querySelector('.js-custom-map-popup-close');

                        if (closeButton) {
                            ['touchstart'].forEach((eventType) => {
                                closeButton.addEventListener(eventType, hidePopup);
                            });
                        }

                        ['click', 'touchstart'].forEach((eventType) => {
                            document.addEventListener(eventType, hidePopup);
                        });
                    }
                }
            }

            viewer.OrbitControls.noZoom = true;

            viewer.add(image);
        }
    };

    if (button) {
        button.addEventListener('click', createCustomMap.bind(this, customMap));
    }
}
