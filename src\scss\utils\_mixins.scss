@use 'sass:math';

%icons-font-aliasing {
    display: inline-block;
    font-family: var(--font-awesome-typo);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-style: normal;
    font-variant: normal;
    font-weight: var(--fw-normal);
    //vertical-align: -0.125em;
}

// Insert custom breakpoint with custom max viewport value.
@mixin media-max($breakpoint, $orientation: false) {
    @if $orientation {
        @media screen and (max-width: #{$breakpoint}px) and (orientation: $orientation) {
            @content;
        }
    }

    @else {
        @media screen and (max-width: #{$breakpoint}px) {
            @content;
        }
    }
}

// Insert custom breakpoint with custom min viewport value.
@mixin media-min($breakpoint) {
    @media screen and (min-width: #{$breakpoint}px) {
        @content;
    }
}

// Add styles for :hover/:focus state
@mixin on-event {
    &:hover,
    &:focus,
    &:focus-within {
        @content;
    }
}

// Clear thread after float;
@mixin clear {
    &::after {
        clear: both;
        content: "";
        display: block;
        height: 0;
        overflow: hidden;
        visibility: hidden;
    }
}

// Set width and height for element
@mixin size($width, $height: $width) {
    height: $height;
    width: $width;
}

// Set max-width and max-height for element
@mixin max-size($width, $height: $width) {
    max-height: $height;
    max-width: $width;
}

// Set min-width and min-height for element
@mixin min-size($width, $height: $width) {
    min-height: $height;
    min-width: $width;
}

// Set transition for element with settings
@mixin trs($prop: all, $duration: 250ms, $easing: ease-in-out, $delay: 0ms) {
    transition: $prop $duration $easing $delay;
}

// Flexbox mixin for setting flex properties
// @param {string} $direction - flex-direction value (row, column, row-reverse, column-reverse)
// @param {string} $wrap - flex-wrap value (nowrap, wrap, wrap-reverse)
// @param {string} $flow - flex-flow shorthand (null to skip)
// @param {string} $justify - justify-content value (flex-start, flex-end, center, space-between, space-around, space-evenly)
// @param {string} $align - align-items value (stretch, flex-start, flex-end, center, baseline)
@mixin flex($direction: row, $wrap: nowrap, $flow: null, $justify: flex-start, $align: stretch) {
    display: flex;

    @if $flow {
        flex-flow: $flow;
    } @else {
        @if $direction {
            flex-direction: $direction;
        }
        @if $wrap {
            flex-wrap: $wrap;
        }
    }

    @if $justify {
        justify-content: $justify;
    }

    @if $align {
        align-items: $align;
    }
}

// Use this for creating scalable elements (usually images / background images) that maintain a ratio.
@mixin responsive-ratio($x, $y, $pseudo) {
    $padding: unquote(math.div($y, $x) * 100 + "%");

    @if $pseudo == "before" {
        &::before {
            content: "";
            display: block;
            padding-top: $padding;
            width: 100%;
        }
    }

    @else if $pseudo == "after" {
        &::after {
            content: "";
            display: block;
            padding-top: $padding;
            width: 100%;
        }
    }

    @else {
        padding-top: $padding;
    }
}

// Mixin for truncating text
// @param {Integer} - $width - row max-width
@mixin truncate($width) {
    max-width: $width;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// Mixin for truncating text on line number
// @param {Integer} - $line - line number
@mixin truncateOnLine($line) {
    -webkit-box-orient: vertical;
    display: -webkit-box;
    -webkit-line-clamp: $line;
    line-clamp: $line;
    overflow: hidden;
    text-overflow: ellipsis;
}

// Set object fit property include polyfill values
// @param {string} $fit - object-fit value
// @param {string} $position - object-fit-position value
@mixin object-fit($fit: "cover", $position: "center") {
    font-family: "object-fit: #{$fit}; object-position: #{$position};";
    object-fit: unquote($fit);
}

// Fix object fit for IE 11 and EDGE
@mixin object-fit-video-ie() {
    // IE
    @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        height: auto;
        width: 100vw;
    }

    // EDGE
    @supports (object-fit: cover) and (-ms-ime-align: auto) {
        height: auto;
        width: 100%;
    }
}

// Add styles if IE 10 and above
@mixin if-ie() {
    @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        @content;
    }
}

// Add styles if EDGE
@mixin if-edge() {
    @supports (-ms-ime-align: auto) {
        @content;
    }
}

// Add styles for multiline underline
@mixin multiline-underline($size: 0.05em, $color: currentColor) {
    @include trs($duration: 300ms);
    background-image: linear-gradient(transparent calc(100% - #{$size}), #{$color} #{$size});
    background-repeat: no-repeat;
    background-size: 0 100%;
    outline: none !important;
    text-decoration: none;
    width: calc(100%);
}

// Set absolute position with settings
@mixin absolute($params...) {
    position: absolute;

    @if length($params) > 0 {
        $params: if(length($params) == 1, nth($params, 1), $params);
        $props: (top, right, bottom, left);

        @for $index from 1 through length($params) {
            #{nth($props, $index)}: nth($params, $index);
        }
    }
}

// Set fixed position with settings
@mixin fixed($params...) {
    position: fixed;

    @if length($params) > 0 {
        $params: if(length($params) == 1, nth($params, 1), $params);
        $props: (top, right, bottom, left);

        @for $index from 1 through length($params) {
            #{nth($props, $index)}: nth($params, $index);
        }
    }
}

// Set relative position with settings
@mixin relative($params...) {
    position: relative;

    @if length($params) > 0 {
        $params: if(length($params) == 1, nth($params, 1), $params);
        $props: (top, right, bottom, left);

        @for $index from 1 through length($params) {
            #{nth($props, $index)}: nth($params, $index);
        }
    }
}

// Reset position to static
@mixin reset-position {
    bottom: auto;
    left: auto;
    position: static;
    right: auto;
    top: auto;
}

// Center element inside block (absolute position)
@mixin abs-center {
    @include absolute(50%, null, null, 50%);
    transform: translate(-50%, -50%);
}

// Center block horizontally
@mixin center-block {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

// Stretches the block from the container to full width
@mixin full-width-block {
    left: 50%;
    margin: 0;
    max-width: 1920px;
    padding: 0;
    position: relative;
    transform: translateX(-50%);
    width: calc(100vw - var(--scrollbar-width));
}

// Create cover block
@mixin coverer {
    @include size(100%);
    @include absolute(0, null, null, 0);
}

// Mixin for creation title decor
// styles: default, primary, center`
@mixin title-decor($style: default) {
    position: relative;
    z-index: 1;

    &::before {
        background-color: var(--color-2--1);
        content: '';
        z-index: -1;
    }

    @if $style == default {
        &::before {
            @include size(68px, 12px);
            @include absolute(null, null, 0, 20px);

            @include breakpoint(medium down) {
                @include size(50px, 8px);
            }

            @include breakpoint(small down) {
                left: 50%;
                transform: translateX(-50%);
            }
        }
    }

    @else if $style == primary {
        &::before {
            @include size(44px, 11px);
            @include absolute(auto, null, -40px, -1px);

            @include breakpoint(small down) {
                bottom: -15px;
            }
        }
    }

    @else if $style == center {
        &::before {
            @include size(68px, 12px);
            @include absolute(null, null, 0, 50%);
            transform: translateX(-50%);
        }
    }
}

// Mixin for creation background patterns with svg
@mixin bg-pattern($color, $opacity: 1, $property: "background-image") {
    $svg: '<svg fill="#{$color}" fill-opacity="#{$opacity}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 3 3" width="3" height="3"><rect width="1" height="1"/></svg>';

    #{$property}: inline-svg($svg);
    background-size: 3px;
}

// Fancy and smooth linear gradient
// @url https://css-tricks.com/easing-linear-gradients/
@mixin scrim-gradient($color: $color-white, $direction: "to top") {
    $scrim-coordinates: (
        0: 1,
        19: 0.738,
        34: 0.541,
        47: 0.382,
        56.5: 0.278,
        65: 0.194,
        73: 0.126,
        80.2: 0.075,
        86.1: 0.042,
        91: 0.021,
        95.2: 0.008,
        98.2: 0.002,
        100: 0
    );

    $stops: ();

    @each $stop, $alpha in $scrim-coordinates {
        $stops: append($stops, rgba($color, $alpha) $stop * 1%, comma);
    }

    background: linear-gradient(unquote($direction), $stops);
}

// get-triangle function
// @url http://apps.eky.hk/css-triangle-generator/
// @param type - one of allowed params listed in $triangles map
// @param - $w - width, optional, only for kind = 2
// @param - $h - height, optional, only for kind = 2
// @param kind - what to get border-color position or border-width? 1 or 2
@function get-triangle($type, $w: 10, $h: 10, $kind: 2) {
    $triangles: (
        "top": (
            "bottom",
            0 ($w*0.5) $h ($w*0.5)
        ),
        "right": (
            "left",
            ($h*0.5) 0 ($h*0.5) $w
        ),
        "bottom": (
            "top",
            $h ($w*0.5) 0 ($w*0.5)
        ),
        "left": (
            "right",
            ($h*0.5) $w ($h*0.5) 0
        ),
        "top-left": (
            "top",
            $h $w 0 0
        ),
        "top-right": (
            "right",
            0 $w $h 0
        ),
        "bottom-right": (
            "bottom",
            0 0 $h $w
        ),
        "bottom-left": (
            "left",
            $h 0 0 $w
        )
    );

    $list: map-get($triangles, $type);

    @if $list == null {
        @error 'Unknown key $type: #{$type}, specify the one of listed in $triangles map.';
    }

    @return nth($list, $kind);
}

@mixin triangle($type, $color, $w, $h) {
    border-color: transparent;
    border-#{get-triangle($type, 0, 0, 1)}-color: $color;
    border-style: solid;
    border-width: get-triangle($type, $w, $h);
    z-index: 1;
}

// Hide text without icon (old-version)
@mixin hide-text {
    overflow: hidden;
    text-align: left;
    text-indent: -9999px;
    white-space: nowrap;
}

// Hide text with icon
@mixin hide-text-with-icon {
    @include hide-text();
    position: relative;

    &::before,
    &::after {
        @include abs-center;
        text-indent: 0;
    }
}

// Set font styles
// @param {string|list} $params - list of font params font-family, font-size, font-weight, font-style
@mixin font($params...) {
    @if length($params) > 0 {
        $params: if(length($params) == 1, nth($params, 1), $params);
        $props: (font-family, font-size, font-weight, font-style);

        @for $index from 1 through length($params) {
            #{nth($props, $index)}: nth($params, $index);
        }
    }
}

// FontFace mixin for inserting custom fonts
@mixin fontFace($fontname, $fontfile, $fontweight: normal, $fontstyle: normal) {
    @font-face {
        font-family: "#{$fontname}";
        font-style: $fontstyle;
        font-weight: $fontweight;
        src:
            url("#{$assets-fonts}#{$fontfile}.woff") format("woff"),
            url("#{$assets-fonts}#{$fontfile}.woff2") format("woff2");
    }
}

// Add inline icon before
@mixin icon($position, $icon, $ff: null, $va: null, $fw: null) {
    &::#{$position} {
        @extend %icons-font-aliasing;
        content: if(str-index($icon, "attr") or str-index($icon, "var"), $icon, unquote('"#{ $icon }"'));
        font-family: $ff;
        font-weight: $fw;
        vertical-align: $va;
    }
}

@mixin icon-before($icon, $ff: null, $va: null, $fw: null) {
    @include icon("before", $icon, $ff, $va, $fw);
}

@mixin icon-after($icon, $ff: null, $va: null, $fw: null) {
    @include icon("after", $icon, $ff, $va, $fw);
}

@mixin focus-outline($outline-width: 2px, $color: currentColor, $offset: -4px) {
    &:focus-visible {
        outline: $outline-width solid $color;
        outline-offset: $offset;
    }
}

//Shadow for section
@mixin section-shadow() {
    position: relative;

    &::before,
    &::after {
        @include size(calc(40% - 30px), 15px);
        background-color: transparent;
        bottom: 20px;
        box-shadow: 0 20px 15px 0 rgba($color-black, 0.5);
        content: '';
        display: block;
        position: absolute;
        z-index: -1;

        @include breakpoint(medium down) {
            width: calc(52% - 30px);
        }

        @include breakpoint(small down) {
            @include size(calc(57% - 32px), 5px);
            box-shadow: 0 20px 5px 0 rgba(0, 0, 0, 0.35);
        }
    }

    &::before {
        left: 70px;
        transform: rotate(-3deg) skewX(-60deg);

        @include breakpoint(medium down) {
            left: 60px;
            transform: rotate(-2deg) skewX(-60deg);
        }

        @include breakpoint(small down) {
            left: 45px;
        }
    }

    &::after {
        right: 70px;
        transform: rotate(3deg) skewX(60deg);
        visibility: visible;

        @include breakpoint(medium down) {
            right: 60px;
            transform: rotate(2deg) skewX(60deg);
        }

        @include breakpoint(small down) {
            right: 45px;
        }
    }
}

// Add styles for font-awesome icon inside block
@mixin fa-icon-style($pseudo: true) {
    span[class*="fa-"] {
        @if $pseudo == true {
            &::before {
                @content;
            }
        }

        @else {
            @content;
        }
    }
}

// Add inverted styles for element
@mixin add-inverted-styles {
    .inverted &,
    .is-inverted &,
    &.is-inverted {
        @content;
    }
}

// Add vissually hidden pattern styles inside @media
@mixin styles-visually-hidden {
    border: 0 !important;
    clip: rect(1px, 1px, 1px, 1px);
    height: 1px !important;
    left: -9999px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    top: auto !important;
    width: 1px !important;
}

// --------- NEW ---------------
// Create helpers from map
@mixin create-utils($property: 'color', $prefix: 'is', $map: (), $extend: false) {
    @each $class, $value in $map {
        @if $extend == false {
            .#{$prefix}-#{$class} {
                #{$property}: $value !important;
            }
        }

        @else {
            &.#{$prefix}-#{$class} {
                #{$property}: $value;
            }
        }
    }
}

// Create helpers from range
@mixin create-range-utils($property: 'height', $prefix: 'is', $range: 10, $step: 10, $extend: false, $propertyClass: false) {
    @for $i from 1 through $range {
        @if $extend == false {
            .#{$prefix}-#{if($propertyClass, $propertyClass, $property)}-#{$i} {
                #{$property}: $step * $i * 1px !important;
            }
        }

        @else {
            &.#{$prefix}-#{if($propertyClass, $propertyClass, $property)}-#{$i} {
                #{$property}: $step * $i * 1px;
            }
        }
    }
}

// Shortcut for extend utils
@mixin create-ext-utils($property: 'color', $prefix: 'is', $map: ()) {
    @include create-utils($property, $prefix, $map, true);
}

// Shortcut for extend range utils
@mixin create-ext-range-utils($property: 'color', $prefix: 'is', $range: 10, $step: 10, $propertyClass: false) {
    @include create-range-utils($property, $prefix, $range, $step, true, $propertyClass);
}

// Hide element on provided breakpoint
@mixin hide-on($breakpoint: small down) {
    @include breakpoint($breakpoint) {
        display: none;
    }
}

// Show element on provided breakpoint
@mixin show-on($breakpoint: small down, $display: block) {
    @include breakpoint($breakpoint) {
        display: $display;
    }
}

// Align the info block to the left and center
@mixin set-infos-middle {
    display: block;
    flex-grow: 0;
    left: 50%;
    //margin: 0;
    position: relative;
    text-align: left;
    transform: translateX(-50%);
    width: auto;
}

@mixin in-content-only {
    .site-content__main:only-child & {
        @content;
    }
}

@mixin in-content-with-sidebar {
    .site-content__main:not(:only-child) & {
        @content;
    }
}

// Line decor
@mixin line-decor($lw: 35px, $lh: 4px, $position: 'before', $color: var(--color-2--1)) {
    &::#{$position} {
        @include size($lw, $lh);
        background-color: $color;
        content: '';
        display: block;
    }
}

// Change color of background svg images
@mixin decor-mask($name, $color, $width, $height: $width, $position: 'before') {
    &::#{$position} {
        @include size($width, $height);
        background-color: $color;
        content: '';
        display: block;
        mask-image: image('icons/#{$name}.svg');
        mask-repeat: no-repeat;
        mask-size: contain;
        pointer-events: none;
    }
}
